import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { SchedulingService } from '../../../core/services/scheduling.service';
import { PatientService } from '../../../core/services/patient.service';
import { DentistService } from '../../../core/services/dentist.service';
import { NotificationService } from '../../../core/services/notification.service';
import { EmployeeService } from '../../../core/services/employee.service';
import { AppointmentCategoriesService } from '../../../appointment-categories/services/appointment-categories.service';
import { ProcedureSelectorComponent } from '../procedure-selector/procedure-selector.component';
import { AppointmentTimePickerComponent } from '../appointment-time-picker/appointment-time-picker.component';
import { fork<PERSON>oin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'app-appointment-form',
  templateUrl: './appointment-form.component.html',
  styleUrls: ['./appointment-form.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    ProcedureSelectorComponent,
    AppointmentTimePickerComponent,
  ],
})
export class AppointmentFormComponent implements OnInit, OnChanges {
  @Input() appointment: any = null; // Agendamento para edição
  @Input() isEditMode = false; // Modo de edição
  @Input() initialDate?: string; // Data inicial
  @Input() initialTime?: string; // Horário inicial
  @Input() initialDentistId?: number; // Dentista inicial
  @Input() showSubmitButtons = true; // Se deve mostrar botões de submit

  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();
  @Output() formValid = new EventEmitter<boolean>();

  schedulingForm: FormGroup;
  isLoading = false;
  isSubmitting = false;
  patients: any[] = [];
  dentists: any[] = [];
  employees: any[] = [];
  appointmentCategories: any[] = [];
  filteredPatients: any[] = [];
  patientSearchTerm = '';
  isNewPatient = false;
  showPatientDropdown = false;

  constructor(
    private fb: FormBuilder,
    private schedulingService: SchedulingService,
    private patientService: PatientService,
    private dentistService: DentistService,
    private notificationService: NotificationService,
    private employeeService: EmployeeService,
    private appointmentCategoriesService: AppointmentCategoriesService
  ) {
    this.schedulingForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('🔄 AppointmentForm: ngOnChanges chamado:', changes);

    if (changes['appointment']) {
      console.log('📥 AppointmentForm: Mudança no appointment detectada:', {
        previousValue: changes['appointment'].previousValue,
        currentValue: changes['appointment'].currentValue,
        firstChange: changes['appointment'].firstChange,
      });

      if (this.appointment) {
        console.log(
          '✅ AppointmentForm: Appointment existe, preenchendo formulário...'
        );

        // Se ainda está carregando dados, aguardar
        if (this.isLoading) {
          console.log(
            '⏳ AppointmentForm: Ainda carregando dados, aguardando...'
          );
          // Aguardar um pouco e tentar novamente
          setTimeout(() => {
            if (!this.isLoading && this.appointment) {
              this.populateFormWithAppointment(this.appointment);
            }
          }, 100);
        } else {
          this.populateFormWithAppointment(this.appointment);

          // Se há procedureIds, aguardar um pouco e tentar preencher novamente
          // (para casos onde os procedimentos são carregados assincronamente)
          if (
            this.appointment.procedureIds &&
            this.appointment.procedureIds.length > 0
          ) {
            setTimeout(() => {
              console.log(
                '🔄 Tentativa adicional de preenchimento dos procedimentos'
              );
              this.schedulingForm
                .get('procedureIds')
                ?.setValue(this.appointment.procedureIds);
            }, 200);
          }
        }
      } else {
        console.warn(
          '⚠️ AppointmentForm: Appointment é null/undefined no ngOnChanges'
        );
      }
    }

    // Verificar se há mudanças em outros inputs importantes
    if (changes['isEditMode']) {
      console.log(
        '🔄 AppointmentForm: Modo de edição mudou:',
        changes['isEditMode'].currentValue
      );
    }
  }

  private createForm(): FormGroup {
    const form = this.fb.group({
      patientId: [''], // Removido Validators.required - validação customizada no isPatientValid()
      dentistId: ['', Validators.required],
      date: ['', Validators.required],
      time: ['', Validators.required],
      endTime: [''],
      duration: [''],
      status: ['unconfirmed'],
      notes: [''],
      email: [''],
      phone: [''],
      isFirstAppointment: [false],
      procedureIds: [[]],
      scheduledBy: [''],
      appointmentCategoryId: [''],
    });

    // Emitir mudanças de validade
    form.statusChanges.subscribe((status) => {
      this.emitFormValidity();
    });

    return form;
  }

  private loadInitialData(): void {
    this.isLoading = true;

    const patients$ = this.patientService.getAllPatients().pipe(
      catchError((error) => {
        console.error('Erro ao carregar pacientes:', error);
        return of([]);
      })
    );

    const dentists$ = this.dentistService.getAllDentists().pipe(
      catchError((error) => {
        console.error('Erro ao carregar dentistas:', error);
        return of([]);
      })
    );

    const employees$ = this.employeeService.getAllEmployees().pipe(
      catchError((error) => {
        console.error('Erro ao carregar funcionários:', error);
        return of([]);
      })
    );

    const appointmentCategories$ = this.appointmentCategoriesService
      .getAllCategories({ isActive: true })
      .pipe(
        catchError((error) => {
          console.error('Erro ao carregar categorias de agendamento:', error);
          return of({ data: [], total: 0 });
        })
      );

    forkJoin({
      patients: patients$,
      dentists: dentists$,
      employees: employees$,
      appointmentCategories: appointmentCategories$,
    }).subscribe({
      next: (result) => {
        this.patients = result.patients;
        this.filteredPatients = [];
        this.dentists = result.dentists;
        this.employees = result.employees;
        this.appointmentCategories = result.appointmentCategories.data;

        // Se há um appointment para preencher, fazer isso após carregar os dados
        if (this.appointment) {
          console.log(
            '📥 AppointmentForm: Preenchendo formulário após carregar dados iniciais'
          );
          this.populateFormWithAppointment(this.appointment);
        }

        this.applyInitialValues();
        this.isLoading = false;

        // Emitir validade inicial após carregar dados
        setTimeout(() => {
          this.emitFormValidity();
        }, 100);
      },
      error: (error) => {
        console.error('Erro ao carregar dados:', error);
        this.isLoading = false;
      },
    });
  }

  private applyInitialValues(): void {
    const updates: any = {};

    if (this.initialDate) {
      updates.date = this.initialDate;
    }

    if (this.initialTime) {
      updates.time = this.initialTime;
    }

    if (this.initialDentistId) {
      updates.dentistId = this.initialDentistId;
    }

    if (Object.keys(updates).length > 0) {
      this.schedulingForm.patchValue(updates);
    }
  }

  private populateFormWithAppointment(appointment: any): void {
    console.log(
      '🔄 AppointmentForm: Preenchendo formulário com appointment:',
      appointment
    );

    if (!appointment) {
      console.warn('⚠️ AppointmentForm: Appointment é null/undefined');
      return;
    }

    const date = new Date(appointment.date);
    const formattedDate = date.toISOString().split('T')[0];

    const patient = this.patients.find((p) => p.id === appointment.patientId);
    if (patient) {
      this.patientSearchTerm = patient.name;
      console.log('✅ AppointmentForm: Paciente encontrado:', patient.name);

      // Preencher dados do paciente no formulário para exibição
      this.schedulingForm.patchValue({
        email: patient.email || appointment.email || '',
        phone: patient.phone || appointment.phone || '',
      });
    } else {
      console.warn(
        '⚠️ AppointmentForm: Paciente não encontrado para ID:',
        appointment.patientId
      );
      // Se não encontrou o paciente, usar o nome do appointment
      this.patientSearchTerm =
        appointment.patientName || 'Paciente não encontrado';

      // Usar dados do appointment se não encontrou o paciente
      this.schedulingForm.patchValue({
        email: appointment.email || '',
        phone: appointment.phone || '',
      });
    }

    const formData = {
      patientId: Number(appointment.patientId),
      dentistId: appointment.dentist?.id ? Number(appointment.dentist.id) : '',
      date: formattedDate,
      time: appointment.startTime,
      endTime: appointment.endTime || '',
      duration: appointment.duration || '',
      status: appointment.status || 'unconfirmed',
      notes: appointment.notes || '',
      appointmentCategoryId: appointment.appointmentCategory?.id || '',
      // Preencher procedimentos se existirem - extrair IDs do array de objetos
      procedureIds: appointment.procedures
        ? appointment.procedures.map((p: any) => p.id)
        : [],
      // Preencher outros campos se existirem
      scheduledBy: appointment.scheduledBy?.id || '',
      isFirstAppointment: appointment.isFirstAppointment || false,
    };

    console.log(
      '📝 AppointmentForm: Dados para preencher o formulário:',
      formData
    );
    console.log(
      '🔍 AppointmentForm: Dados completos do appointment:',
      appointment
    );
    console.log('🔍 AppointmentForm: Dentist encontrado:', appointment.dentist);
    console.log(
      '🔍 AppointmentForm: DentistId extraído:',
      appointment.dentist?.id
    );
    console.log(
      '🔍 AppointmentForm: ScheduledBy encontrado:',
      appointment.scheduledBy
    );
    console.log(
      '🔍 AppointmentForm: ScheduledBy ID extraído:',
      appointment.scheduledBy?.id
    );
    console.log(
      '🔍 AppointmentForm: Procedures encontrados:',
      appointment.procedures
    );
    console.log(
      '🔍 AppointmentForm: ProcedureIds extraídos:',
      appointment.procedures ? appointment.procedures.map((p: any) => p.id) : []
    );
    console.log(
      '🔍 AppointmentForm: Tipo do DentistId:',
      typeof appointment.dentist?.id
    );

    this.schedulingForm.patchValue(formData);

    // Forçar preenchimento específico dos campos problemáticos
    if (appointment.dentist?.id) {
      console.log(
        '🔧 Forçando preenchimento do dentistId:',
        appointment.dentist.id
      );

      // Aguardar que os dentistas sejam carregados antes de definir o valor
      this.retrySetDentistId(Number(appointment.dentist.id), 0);
    }

    // Verificar se há procedimentos e extrair os IDs
    const procedureIds = appointment.procedures
      ? appointment.procedures.map((p: any) => p.id)
      : [];
    if (procedureIds && procedureIds.length > 0) {
      console.log('🔧 Forçando preenchimento dos procedureIds:', procedureIds);

      // Aguardar múltiplas tentativas para garantir que o procedure-selector esteja pronto
      this.retrySetProcedureIds(procedureIds, 0);
    }

    console.log(
      '✅ AppointmentForm: Formulário preenchido. Valores atuais:',
      this.schedulingForm.value
    );

    // Aguardar um pouco e verificar se os campos foram preenchidos corretamente
    setTimeout(() => {
      console.log('🔍 AppointmentForm: Verificação após patchValue:');
      console.log(
        '- DentistId no form:',
        this.schedulingForm.get('dentistId')?.value
      );
      console.log(
        '- ScheduledBy no form:',
        this.schedulingForm.get('scheduledBy')?.value
      );
      console.log(
        '- ProcedureIds no form:',
        this.schedulingForm.get('procedureIds')?.value
      );
      console.log(
        '- Dentistas disponíveis:',
        this.dentists.map((d) => ({ id: d.id, name: d.name }))
      );
      console.log(
        '- Funcionários disponíveis:',
        this.employees.map((e) => ({ id: e.id, name: e.name }))
      );
    }, 100);
  }

  private retrySetProcedureIds(procedureIds: number[], attempt: number): void {
    const maxAttempts = 5;
    const delay = 200 * (attempt + 1); // Aumentar delay a cada tentativa

    setTimeout(() => {
      console.log(
        `🔄 Tentativa ${attempt + 1} de definir procedureIds:`,
        procedureIds
      );

      this.schedulingForm.get('procedureIds')?.setValue(procedureIds);

      // Verificar se foi definido corretamente
      const currentValue = this.schedulingForm.get('procedureIds')?.value;
      console.log('🔍 Valor atual dos procedureIds no form:', currentValue);

      // Se ainda não foi definido corretamente e não atingiu o máximo de tentativas
      if (
        (!currentValue || currentValue.length === 0) &&
        attempt < maxAttempts - 1
      ) {
        console.log(
          `⚠️ ProcedureIds não foram definidos, tentando novamente em ${delay}ms...`
        );
        this.retrySetProcedureIds(procedureIds, attempt + 1);
      } else if (currentValue && currentValue.length > 0) {
        console.log('✅ ProcedureIds definidos com sucesso!');
      } else {
        console.warn(
          '❌ Falha ao definir procedureIds após todas as tentativas'
        );
      }
    }, delay);
  }

  private retrySetDentistId(dentistId: number, attempt: number): void {
    const maxAttempts = 3;
    const delay = 200 * (attempt + 1);

    setTimeout(() => {
      console.log(
        `🔄 Tentativa ${attempt + 1} de definir dentistId:`,
        dentistId
      );

      // Verificar se o dentista existe na lista
      const dentistExists = this.dentists.some((d) => d.id === dentistId);
      console.log('🔍 Dentista existe na lista?', dentistExists);
      console.log(
        '🔍 Dentistas disponíveis:',
        this.dentists.map((d) => ({ id: d.id, name: d.name }))
      );

      if (dentistExists) {
        this.schedulingForm.get('dentistId')?.setValue(dentistId);
        console.log('✅ DentistId definido com sucesso!');
      } else if (attempt < maxAttempts - 1) {
        console.log(
          `⚠️ Dentista não encontrado na lista, tentando novamente em ${delay}ms...`
        );
        this.retrySetDentistId(dentistId, attempt + 1);
      } else {
        console.warn('❌ Dentista não encontrado após todas as tentativas');
      }
    }, delay);
  }

  onSubmit(): void {
    if (!this.isPatientValid()) {
      this.schedulingForm.get('patientId')?.markAsTouched();
      return;
    }

    if (this.schedulingForm.invalid) {
      Object.keys(this.schedulingForm.controls).forEach((key) => {
        const control = this.schedulingForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formValues = this.schedulingForm.value;

    if (this.isNewPatient) {
      this.createPatientAndEmitData(formValues);
    } else {
      const schedulingData = this.prepareSchedulingData(
        formValues,
        formValues.patientId
      );
      this.emitFormData(schedulingData);
    }
  }

  private createPatientAndEmitData(formValues: any): void {
    const patientData = {
      name: this.patientSearchTerm.trim(),
      phone: formValues.phone || '',
    };

    this.patientService.createExternalPatient(patientData).subscribe({
      next: (newPatient) => {
        const schedulingData = this.prepareSchedulingData(
          formValues,
          newPatient.id
        );
        this.emitFormData(schedulingData);
      },
      error: (error) => {
        console.error('Erro ao criar paciente:', error);
        this.notificationService.error(
          'Erro ao criar paciente. Por favor, tente novamente.'
        );
        this.isSubmitting = false;
      },
    });
  }

  private prepareSchedulingData(formValues: any, patientId: number): any {
    const data: any = {
      patientId: Number(patientId),
      date: formValues.date,
      time: formValues.time,
      status: formValues.status || 'unconfirmed',
      notes: formValues.notes || '',
      email: formValues.email || '',
      phone: formValues.phone || '',
      isFirstAppointment: formValues.isFirstAppointment || false,
    };

    // Adicionar campos opcionais apenas se tiverem valores válidos
    if (formValues.dentistId && formValues.dentistId !== '') {
      data.dentistId = Number(formValues.dentistId);
    }

    if (
      formValues.appointmentCategoryId &&
      formValues.appointmentCategoryId !== ''
    ) {
      data.appointmentCategoryId = Number(formValues.appointmentCategoryId);
    }

    if (formValues.duration) {
      data.duration = Number(formValues.duration);
    }

    if (formValues.endTime) {
      data.endTime = formValues.endTime;
    }

    if (formValues.procedureIds && formValues.procedureIds.length > 0) {
      data.procedureIds = formValues.procedureIds.map((id: any) => Number(id));
    }

    if (formValues.scheduledBy && formValues.scheduledBy !== '') {
      data.scheduledBy = formValues.scheduledBy;
    }

    return data;
  }

  private emitFormData(data: any): void {
    console.log('Dados preparados para envio:', data);
    this.formSubmit.emit(data);
    this.isSubmitting = false;
  }

  cancel(): void {
    this.formCancel.emit();
  }

  // Métodos para busca de pacientes
  filterPatients(): void {
    if (this.isEditMode) {
      this.filteredPatients = [];
      this.showPatientDropdown = false;
      return;
    }

    if (this.patientSearchTerm.length < 3) {
      this.filteredPatients = [];
      this.showPatientDropdown = false;
      this.isNewPatient = false;
      this.schedulingForm.patchValue({ patientId: '' });
      this.emitFormValidity(); // Emitir validade quando campo fica inválido
      return;
    }

    const searchTerm = this.patientSearchTerm.toLowerCase();
    this.filteredPatients = this.patients.filter(
      (patient) =>
        patient.name.toLowerCase().includes(searchTerm) ||
        (patient.cpf && patient.cpf.includes(searchTerm))
    );

    this.showPatientDropdown = true;

    // Emitir validade quando há texto suficiente para novo paciente
    if (this.patientSearchTerm.length >= 3) {
      this.emitFormValidity();
    }
  }

  selectPatient(patient: any): void {
    this.schedulingForm.patchValue({
      patientId: Number(patient.id),
      email: patient.email || '',
      phone: patient.phone || '',
    });
    this.patientSearchTerm = patient.name;
    this.filteredPatients = [];
    this.showPatientDropdown = false;
    this.isNewPatient = false;

    // Emitir validade após selecionar paciente
    setTimeout(() => {
      this.emitFormValidity();
    }, 50);
  }

  selectNewPatient(): void {
    this.isNewPatient = true;
    this.filteredPatients = [];
    this.showPatientDropdown = false;
    this.schedulingForm.patchValue({ patientId: null });

    // Emitir validade após selecionar novo paciente
    setTimeout(() => {
      this.emitFormValidity();
    }, 50);
  }

  onPatientInputFocus(): void {
    if (!this.isEditMode && this.patientSearchTerm.length >= 3) {
      this.showPatientDropdown = true;
    }
  }

  onPatientInputBlur(): void {
    setTimeout(() => {
      this.showPatientDropdown = false;
    }, 200);
  }

  onTimeSelected(timeSelection: { startTime: string; endTime: string }): void {
    const duration = this.calculateDuration(
      timeSelection.startTime,
      timeSelection.endTime
    );

    this.schedulingForm.patchValue({
      time: timeSelection.startTime,
      endTime: timeSelection.endTime,
      duration: duration,
    });

    // Emitir validade após selecionar horário
    setTimeout(() => {
      this.emitFormValidity();
    }, 50);
  }

  private calculateDuration(startTime: string, endTime: string): number {
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    return endMinutes - startMinutes;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Getters para facilitar acesso aos controles
  get dentistIdControl() {
    return this.schedulingForm.get('dentistId');
  }

  get dateControl() {
    return this.schedulingForm.get('date');
  }

  get timeControl() {
    return this.schedulingForm.get('time');
  }

  // Validação customizada para paciente
  isPatientValid(): boolean {
    return this.isNewPatient
      ? this.patientSearchTerm.trim().length >= 3
      : !!this.schedulingForm.get('patientId')?.value;
  }

  // Método para emitir a validade do formulário
  emitFormValidity(): void {
    const formStatus = this.schedulingForm.status;
    const patientValid = this.isPatientValid();
    const isValid = formStatus === 'VALID' && patientValid;

    console.log('🔍 AppointmentForm: emitFormValidity chamado:', {
      formStatus,
      patientValid,
      isValid,
      formValues: this.schedulingForm.value,
      formErrors: this.getFormErrors(),
      patientSearchTerm: this.patientSearchTerm,
      isNewPatient: this.isNewPatient,
    });

    this.formValid.emit(isValid);
  }

  // Método auxiliar para obter erros do formulário
  private getFormErrors(): any {
    const errors: any = {};
    Object.keys(this.schedulingForm.controls).forEach((key) => {
      const control = this.schedulingForm.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }
}
