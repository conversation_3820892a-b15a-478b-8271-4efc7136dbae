import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DentistScheduleService } from '../../../core/services/dentist-schedule.service';
import { NotificationService } from '../../../core/services/notification.service';

interface TimeSlot {
  time: string;
  status:
    | 'unavailable'
    | 'available'
    | 'selected'
    | 'conflict'
    | 'occupied'
    | 'out_of_office';
  isInWorkingHours: boolean;
  appointmentInfo?: {
    id: number;
    patientName: string;
    startTime: string;
    endTime: string;
    duration: number;
    status: string;
  };
}

interface TimeSelection {
  selectedSlots: string[]; // Array de horários selecionados em ordem
}

@Component({
  selector: 'app-appointment-time-picker',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './appointment-time-picker.component.html',
  styleUrls: ['./appointment-time-picker.component.scss'],
})
export class AppointmentTimePickerComponent implements OnInit, OnChanges {
  @Input() dentistId: number | null = null;
  @Input() date: string = '';
  @Input() duration: number = 30; // duração em minutos
  @Input() excludeAppointmentId?: number;
  @Input() initialStartTime?: string; // horário inicial para pré-seleção
  @Input() initialEndTime?: string; // horário final para pré-seleção

  @Output() timeSelected = new EventEmitter<{
    startTime: string;
    endTime: string;
  }>();

  // Dados do grid
  morningSlots: TimeSlot[] = [];
  afternoonSlots: TimeSlot[] = [];

  // Estado da seleção
  selection: TimeSelection = { selectedSlots: [] };

  // Estado do componente
  isLoading = false;

  constructor(
    private dentistScheduleService: DentistScheduleService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.initializeTimeSlots();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['dentistId'] || changes['date']) {
      this.resetSelection();
      if (this.dentistId && this.date) {
        this.loadAvailableTimes();
      } else {
        this.setAllSlotsUnavailable();
      }
    }

    // Verificar se os horários iniciais mudaram
    if (changes['initialStartTime'] || changes['initialEndTime']) {
      if (this.initialStartTime && this.initialEndTime) {
        // Aguardar um pouco para garantir que os slots foram carregados
        setTimeout(() => {
          this.preselectTimeRange(this.initialStartTime!, this.initialEndTime!);
        }, 100);
      }
    }
  }

  private initializeTimeSlots(): void {
    // Manhã: 08:30 até 12:55 (incluindo 12:55)
    this.morningSlots = this.generateTimeSlots('08:30', '12:55');
    // Tarde: 13:00 até 18:55 (incluindo 18:55)
    this.afternoonSlots = this.generateTimeSlots('13:00', '18:55');
  }

  private generateTimeSlots(startTime: string, endTime: string): TimeSlot[] {
    const slots: TimeSlot[] = [];
    const start = this.timeToMinutes(startTime);
    const end = this.timeToMinutes(endTime);

    // Incluir o horário final
    for (let minutes = start; minutes <= end; minutes += 5) {
      const time = this.minutesToTime(minutes);
      slots.push({
        time,
        status: 'unavailable',
        isInWorkingHours: false,
      });
    }

    return slots;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins
      .toString()
      .padStart(2, '0')}`;
  }

  private loadAvailableTimes(): void {
    if (!this.dentistId || !this.date) return;

    this.isLoading = true;
    this.dentistScheduleService
      .getScheduleDetails(
        this.dentistId,
        this.date,
        5 // intervalo de 5 minutos
      )
      .subscribe({
        next: (response) => {
          this.updateSlotsFromScheduleDetails(response);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erro ao carregar horários:', error);
          this.notificationService.error(
            'Erro ao carregar horários disponíveis'
          );
          this.setAllSlotsUnavailable();
          this.isLoading = false;
        },
      });
  }

  private updateSlotsFromScheduleDetails(scheduleDetails: any): void {
    // Criar um mapa dos slots do backend para acesso rápido
    const backendSlotsMap = new Map();
    scheduleDetails.timeSlots.forEach((slot: any) => {
      backendSlotsMap.set(slot.time, slot);
    });

    // Atualizar slots da manhã
    this.morningSlots.forEach((slot) => {
      const backendSlot = backendSlotsMap.get(slot.time);
      if (backendSlot) {
        slot.status = this.mapBackendStatus(backendSlot.status);
        slot.isInWorkingHours = backendSlot.isInWorkingHours;
        slot.appointmentInfo = backendSlot.appointmentInfo;

        // Excluir o agendamento atual se estiver editando
        if (
          this.excludeAppointmentId &&
          slot.appointmentInfo?.id === this.excludeAppointmentId
        ) {
          slot.status = 'available';
          slot.appointmentInfo = undefined;
        }
      } else {
        slot.status = 'unavailable';
        slot.isInWorkingHours = false;
      }
    });

    // Atualizar slots da tarde
    this.afternoonSlots.forEach((slot) => {
      const backendSlot = backendSlotsMap.get(slot.time);
      if (backendSlot) {
        slot.status = this.mapBackendStatus(backendSlot.status);
        slot.isInWorkingHours = backendSlot.isInWorkingHours;
        slot.appointmentInfo = backendSlot.appointmentInfo;

        // Excluir o agendamento atual se estiver editando
        if (
          this.excludeAppointmentId &&
          slot.appointmentInfo?.id === this.excludeAppointmentId
        ) {
          slot.status = 'available';
          slot.appointmentInfo = undefined;
        }
      } else {
        slot.status = 'unavailable';
        slot.isInWorkingHours = false;
      }
    });

    // Se há horários iniciais definidos, pré-selecionar após carregar os dados
    if (this.initialStartTime && this.initialEndTime) {
      setTimeout(() => {
        this.preselectTimeRange(this.initialStartTime!, this.initialEndTime!);
      }, 50);
    }
  }

  private mapBackendStatus(
    backendStatus: string
  ):
    | 'unavailable'
    | 'available'
    | 'selected'
    | 'conflict'
    | 'occupied'
    | 'out_of_office' {
    switch (backendStatus) {
      case 'available':
        return 'available';
      case 'occupied':
        return 'occupied';
      case 'out_of_office':
        return 'out_of_office';
      default:
        return 'unavailable';
    }
  }

  private setAllSlotsUnavailable(): void {
    [...this.morningSlots, ...this.afternoonSlots].forEach((slot) => {
      slot.status = 'unavailable';
      slot.isInWorkingHours = false;
    });
  }

  onSlotClick(slot: TimeSlot): void {
    if (this.isLoading) return;

    // Se não há dentista selecionado
    if (!this.dentistId) {
      this.notificationService.warning('Selecione um dentista primeiro');
      return;
    }

    // Se o slot não está em horário de trabalho
    if (!slot.isInWorkingHours) {
      if (slot.status === 'out_of_office') {
        this.notificationService.warning('Horário de intervalo/almoço');
      } else {
        this.notificationService.warning(
          'Horário fora do expediente do profissional'
        );
      }
      return;
    }

    // Se o slot está ocupado
    if (slot.status === 'occupied') {
      const patientName =
        slot.appointmentInfo?.patientName || 'Paciente não identificado';
      this.notificationService.warning(`Horário ocupado por: ${patientName}`);
      return;
    }

    const clickedTime = slot.time;
    const isCurrentlySelected =
      this.selection.selectedSlots.includes(clickedTime);

    if (isCurrentlySelected) {
      // Desmarcar célula (apenas se for sequencial)
      this.handleDeselection(clickedTime);
    } else {
      // Marcar célula
      this.handleSelection(clickedTime);
    }

    this.updateSelectionDisplay();
    this.emitSelectionIfValid();
  }

  private handleSelection(clickedTime: string): void {
    if (this.selection.selectedSlots.length === 0) {
      // Primeira seleção
      this.selection.selectedSlots = [clickedTime];
    } else {
      // Adicionar nova seleção e preencher gaps
      const allSlots = this.getAllTimeSlots();
      const currentSlots = this.selection.selectedSlots.sort(
        (a, b) => this.timeToMinutes(a) - this.timeToMinutes(b)
      );

      const clickedMinutes = this.timeToMinutes(clickedTime);
      const firstMinutes = this.timeToMinutes(currentSlots[0]);
      const lastMinutes = this.timeToMinutes(
        currentSlots[currentSlots.length - 1]
      );

      if (clickedMinutes < firstMinutes) {
        // Clicou antes do início - preencher do clique até o início
        this.fillSlotsBetween(clickedTime, currentSlots[0], allSlots);
      } else if (clickedMinutes > lastMinutes) {
        // Clicou depois do fim - preencher do fim até o clique
        this.fillSlotsBetween(
          currentSlots[currentSlots.length - 1],
          clickedTime,
          allSlots
        );
      } else {
        // Clicou no meio - apenas adicionar se não estiver selecionado
        if (!this.selection.selectedSlots.includes(clickedTime)) {
          this.selection.selectedSlots.push(clickedTime);
        }
      }
    }
  }

  private handleDeselection(clickedTime: string): void {
    const sortedSlots = this.selection.selectedSlots.sort(
      (a, b) => this.timeToMinutes(a) - this.timeToMinutes(b)
    );

    const clickedIndex = sortedSlots.indexOf(clickedTime);

    // Só permite desmarcar se for o primeiro ou último da sequência
    if (clickedIndex === 0 || clickedIndex === sortedSlots.length - 1) {
      this.selection.selectedSlots = this.selection.selectedSlots.filter(
        (slot) => slot !== clickedTime
      );
    } else {
      this.notificationService.warning(
        'Só é possível desmarcar células do início ou fim da seleção'
      );
    }
  }

  private fillSlotsBetween(
    startTime: string,
    endTime: string,
    allSlots: string[]
  ): void {
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);

    const slotsToAdd = allSlots.filter((slot) => {
      const slotMinutes = this.timeToMinutes(slot);
      return slotMinutes >= startMinutes && slotMinutes <= endMinutes;
    });

    // Adicionar apenas slots que não estão já selecionados
    slotsToAdd.forEach((slot) => {
      if (!this.selection.selectedSlots.includes(slot)) {
        this.selection.selectedSlots.push(slot);
      }
    });
  }

  private getAllTimeSlots(): string[] {
    return [...this.morningSlots, ...this.afternoonSlots].map(
      (slot) => slot.time
    );
  }

  private findSlotByTime(time: string): TimeSlot | undefined {
    return [...this.morningSlots, ...this.afternoonSlots].find(
      (slot) => slot.time === time
    );
  }

  private emitSelectionIfValid(): void {
    if (this.selection.selectedSlots.length === 0) {
      return;
    }

    const sortedSlots = this.selection.selectedSlots.sort(
      (a, b) => this.timeToMinutes(a) - this.timeToMinutes(b)
    );

    const startTime = sortedSlots[0];
    const endTime = this.getEndTimeFromSlot(
      sortedSlots[sortedSlots.length - 1]
    );

    // Verificar se há conflitos
    const hasConflict = this.checkForConflicts();

    if (hasConflict) {
      this.confirmConflictSelection();
    } else {
      this.timeSelected.emit({
        startTime,
        endTime,
      });
    }
  }

  private getEndTimeFromSlot(slotTime: string): string {
    const slotMinutes = this.timeToMinutes(slotTime);
    return this.minutesToTime(slotMinutes + 5); // Adicionar 5 minutos
  }

  private checkForConflicts(): boolean {
    // Verificar se algum slot selecionado está ocupado (tem agendamento)
    return this.selection.selectedSlots.some((slotTime) => {
      const slot = this.findSlotByTime(slotTime);
      return slot && slot.status === 'occupied';
    });
  }

  private confirmConflictSelection(): void {
    const confirmed = confirm(
      'Já existe um agendamento em parte do horário selecionado. Deseja continuar mesmo assim?'
    );

    if (confirmed) {
      this.markAsConflict();
      const sortedSlots = this.selection.selectedSlots.sort(
        (a, b) => this.timeToMinutes(a) - this.timeToMinutes(b)
      );
      this.timeSelected.emit({
        startTime: sortedSlots[0],
        endTime: this.getEndTimeFromSlot(sortedSlots[sortedSlots.length - 1]),
      });
    } else {
      this.resetSelection();
    }
  }

  private markAsConflict(): void {
    this.updateSelectionDisplay('conflict');
  }

  private updateSelectionDisplay(
    status: 'selected' | 'conflict' = 'selected'
  ): void {
    // Resetar todos os slots selecionados para seu status original
    [...this.morningSlots, ...this.afternoonSlots].forEach((slot) => {
      if (slot.status === 'selected' || slot.status === 'conflict') {
        // Restaurar o status original baseado no backend
        if (slot.isInWorkingHours) {
          slot.status = slot.appointmentInfo ? 'occupied' : 'available';
        } else {
          slot.status = 'out_of_office';
        }
      }
    });

    // Marcar slots selecionados
    [...this.morningSlots, ...this.afternoonSlots].forEach((slot) => {
      if (this.selection.selectedSlots.includes(slot.time)) {
        slot.status = status;
      }
    });
  }

  resetSelection(): void {
    this.selection = { selectedSlots: [] };

    // Resetar status dos slots para o status original
    [...this.morningSlots, ...this.afternoonSlots].forEach((slot) => {
      if (slot.status === 'selected' || slot.status === 'conflict') {
        // Restaurar o status original baseado no backend
        if (slot.isInWorkingHours) {
          slot.status = slot.appointmentInfo ? 'occupied' : 'available';
        } else {
          slot.status = 'out_of_office';
        }
      }
    });
  }

  getSlotClasses(slot: TimeSlot): string {
    const baseClasses =
      'h-8 border border-dashed border-gray-300 cursor-pointer flex items-center justify-center transition-colors relative';

    switch (slot.status) {
      case 'unavailable':
        return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;
      case 'available':
        return `${baseClasses} bg-white hover:bg-gray-100`;
      case 'selected':
        return `${baseClasses} bg-cyan-500 text-white`;
      case 'conflict':
        return `${baseClasses} bg-blue-700 text-white`;
      case 'occupied':
        return `${baseClasses} bg-rose-300 text-rose-900 hover:bg-rose-400 cursor-not-allowed`;
      case 'out_of_office':
        return `${baseClasses} bg-gray-200 text-gray-500 cursor-not-allowed`;
      default:
        return `${baseClasses} bg-gray-300`;
    }
  }

  getLabelClasses(slot: TimeSlot): string {
    if (this.shouldShowLabel(slot)) {
      // Labels podem se estender além da célula
      return 'text-[10px] whitespace-nowrap absolute left-0 z-10 pointer-events-none';
    }
    return 'text-xs';
  }

  shouldShowLabel(slot: TimeSlot): boolean {
    const minutes = this.timeToMinutes(slot.time);
    return minutes % 30 === 0; // Mostrar label a cada 30 minutos
  }

  getSlotLabel(slot: TimeSlot): string {
    if (!this.shouldShowLabel(slot)) return '';
    return slot.time.substring(0, 5); // HH:MM
  }

  trackByTime(index: number, slot: TimeSlot): string {
    return slot.time;
  }

  getEmptySlots(currentLength: number, targetLength: number): number[] {
    const emptyCount = Math.max(0, targetLength - currentLength);
    return Array(emptyCount)
      .fill(0)
      .map((_, i) => i);
  }

  getSelectionSummary(): string {
    if (this.selection.selectedSlots.length === 0) return '';

    const sortedSlots = this.selection.selectedSlots.sort(
      (a, b) => this.timeToMinutes(a) - this.timeToMinutes(b)
    );

    const startTime = sortedSlots[0];
    const endTime = this.getEndTimeFromSlot(
      sortedSlots[sortedSlots.length - 1]
    );

    // Calcular duração correta: diferença entre horário final e inicial
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    const duration = endMinutes - startMinutes;

    return `${startTime} até ${endTime} (${duration} minutos)`;
  }

  // Método para pré-selecionar um intervalo de tempo (usado na edição)
  private preselectTimeRange(startTime: string, endTime: string): void {
    console.log('🕐 Pré-selecionando horário:', { startTime, endTime });

    // Resetar seleção atual
    this.resetSelection();

    // Calcular todos os slots que devem ser selecionados
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);

    const allSlots = this.getAllTimeSlots();
    const slotsToSelect = allSlots.filter((slotTime) => {
      const slotMinutes = this.timeToMinutes(slotTime);
      // Incluir slots que estão dentro do intervalo (excluindo o endTime)
      return slotMinutes >= startMinutes && slotMinutes < endMinutes;
    });

    console.log('🎯 Slots a serem selecionados:', slotsToSelect);

    // Verificar se todos os slots estão disponíveis ou são do próprio agendamento
    const unavailableSlots = slotsToSelect.filter((slotTime) => {
      const slot = this.findSlotByTime(slotTime);
      return slot && !slot.isInWorkingHours && slot.status !== 'available';
    });

    if (unavailableSlots.length > 0) {
      console.warn('⚠️ Alguns slots não estão disponíveis:', unavailableSlots);
    }

    // Selecionar os slots
    this.selection.selectedSlots = [...slotsToSelect];
    this.updateSelectionDisplay();

    // Emitir o evento de seleção
    if (this.selection.selectedSlots.length > 0) {
      this.timeSelected.emit({
        startTime,
        endTime,
      });
    }

    console.log('✅ Pré-seleção concluída:', this.selection.selectedSlots);
  }
}
